#!/usr/bin/env python3
"""
Demo script to show what the enhanced alert messages will look like
This simulates the Discord embed format in text
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
import pytz
from bot import WORLD_BOSSES, IRAN_TIMEZONE

def format_embed_demo(title, description, color, fields, footer):
    """Format a Discord embed as text for demonstration"""
    color_names = {
        0xffaa00: "ORANGE",
        0xff6600: "DARK_ORANGE", 
        0xff0000: "RED"
    }
    
    print(f"\n{'='*60}")
    print(f"DISCORD EMBED - {color_names.get(color, 'UNKNOWN')} COLOR")
    print(f"{'='*60}")
    print(f"TITLE: {title}")
    print(f"DESCRIPTION: {description}")
    print("-" * 60)
    
    for field in fields:
        name = field['name']
        value = field['value']
        inline = field.get('inline', False)
        
        print(f"FIELD: {name}")
        print(f"VALUE: {value}")
        if inline:
            print("(INLINE)")
        print("-" * 40)
    
    print(f"FOOTER: {footer}")
    print(f"{'='*60}")

def demo_alert_message(boss_id, alert_type):
    """Demonstrate what an enhanced alert message looks like"""
    boss = WORLD_BOSSES.get(boss_id)
    if not boss:
        print(f"❌ Boss {boss_id} not found")
        return
    
    # Simulate next spawn time (45 minutes from now for demo)
    now = datetime.now(IRAN_TIMEZONE)
    if alert_type == "10min":
        next_spawn = now + timedelta(minutes=10)
    elif alert_type == "5min":
        next_spawn = now + timedelta(minutes=5)
    else:  # spawn
        next_spawn = now
    
    # Create the enhanced embed data
    fields = []
    
    if alert_type == "10min":
        title = f"🔔 **10 MINUTE WARNING**"
        description = f"{boss['emoji']} **{boss['name']}** spawns in **10 minutes**!\n\n*Time to start heading to the location!*"
        color = 0xffaa00
    elif alert_type == "5min":
        title = f"⚠️ **5 MINUTE WARNING**"
        description = f"{boss['emoji']} **{boss['name']}** spawns in **5 minutes**!\n\n*Get ready, almost time!*"
        color = 0xff6600
    else:  # spawn
        title = f"🚨 **SPAWNING NOW!** 🚨"
        description = f"# {boss['emoji']} **{boss['name']}** IS SPAWNING NOW!\n\n**🏃‍♂️ GO GO GO! The fight has begun!**"
        color = 0xff0000
    
    # Basic information fields
    fields.append({
        'name': "📍 Location",
        'value': f"**{boss['map']}**",
        'inline': True
    })
    
    fields.append({
        'name': "⏱️ Duration",
        'value': f"**{boss['duration']} minutes**",
        'inline': True
    })
    
    fields.append({
        'name': "🎯 Difficulty",
        'value': f"**{boss.get('difficulty', 'Unknown')}**",
        'inline': True
    })
    
    # Waypoint information
    if boss.get('waypoint'):
        waypoint_text = f"**{boss.get('waypoint_name', 'Waypoint')}**\n`{boss['waypoint']}`\n*Click to copy waypoint code*"
        fields.append({
            'name': "🗺️ Waypoint (Click to Copy)",
            'value': waypoint_text,
            'inline': False
        })
    
    # Enhanced information for spawn alerts
    if alert_type == "spawn":
        # Fighting tips
        if boss.get('tips'):
            tips_text = "\n".join(boss['tips'][:3])  # Show first 3 tips
            fields.append({
                'name': "⚔️ Fighting Tips",
                'value': tips_text,
                'inline': False
            })
        
        # Recommended gear
        if boss.get('recommended_gear'):
            fields.append({
                'name': "🛡️ Recommended Gear",
                'value': f"*{boss['recommended_gear']}*",
                'inline': False
            })
        
        # Rewards
        if boss.get('rewards'):
            fields.append({
                'name': "🎁 Rewards",
                'value': f"*{boss['rewards']}*",
                'inline': False
            })
    
    # Timing information
    spawn_time_str = next_spawn.strftime("%H:%M Iran Time")
    if alert_type == "spawn":
        fields.append({
            'name': "⏰ Event Time",
            'value': f"**{spawn_time_str}** - **NOW ACTIVE!**",
            'inline': False
        })
    else:
        fields.append({
            'name': "⏰ Spawn Time",
            'value': f"**{spawn_time_str}**",
            'inline': False
        })
    
    # Pre-event information for early warnings
    if alert_type in ["10min", "5min"] and boss.get('pre_event_time', 0) > 0:
        fields.append({
            'name': "⚡ Pre-Events",
            'value': f"Start **{boss['pre_event_time']} minutes** before main boss",
            'inline': False
        })
    
    # Footer
    if alert_type == "spawn":
        footer = "🔥 ACTIVE NOW! Join the fight! • GW2 World Boss Timer"
    else:
        footer = "GW2 World Boss Timer • Get ready!"
    
    # Display the demo
    format_embed_demo(title, description, color, fields, footer)
    
    # For spawn alerts, show the separate waypoint message
    if alert_type == "spawn" and boss.get('waypoint'):
        print(f"\nSEPARATE MESSAGE:")
        print(f"📍 **Quick Waypoint Copy**: {boss['waypoint']}")

def main():
    print("🎮 Enhanced Discord Bot Alert System Demo")
    print("This shows what the new alert messages will look like")
    print("=" * 80)
    
    # Demo different alert types for Tequatl
    print("\n🐲 DEMO: Tequatl the Sunless Alerts")
    demo_alert_message("tequatl_the_sunless", "10min")
    demo_alert_message("tequatl_the_sunless", "5min")
    demo_alert_message("tequatl_the_sunless", "spawn")
    
    # Demo a new boss
    print("\n\n🧊 DEMO: Claw of Jormag (New Boss) Spawn Alert")
    demo_alert_message("claw_of_jormag", "spawn")
    
    # Demo another new boss
    print("\n\n🎭 DEMO: Twisted Marionette (New Boss) Spawn Alert")
    demo_alert_message("twisted_marionette", "spawn")
    
    print("\n\n🎉 Demo completed!")
    print("=" * 80)
    print("Key Enhancements:")
    print("✅ Rich visual formatting with emojis and bold text")
    print("✅ Click-to-copy waypoint codes")
    print("✅ Fighting tips and strategies")
    print("✅ Recommended gear information")
    print("✅ Reward details")
    print("✅ Enhanced 'spawn now' messages")
    print("✅ 6 new world bosses added")
    print("✅ Complete world coverage")

if __name__ == "__main__":
    main()
