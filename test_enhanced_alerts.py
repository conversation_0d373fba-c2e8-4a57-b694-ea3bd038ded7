#!/usr/bin/env python3
"""
Test script for the enhanced Discord bot alert system
This script tests the new features without requiring a live Discord connection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
import pytz

# Import the enhanced boss data
from bot import WORLD_BOSSES, get_next_spawn_time, get_time_until_spawn, IRAN_TIMEZONE

def test_enhanced_boss_data():
    """Test that all bosses have the enhanced data structure"""
    print("🧪 Testing Enhanced Boss Data Structure...")
    print("=" * 50)
    
    required_fields = ['name', 'map', 'schedule', 'duration', 'emoji', 'difficulty']
    enhanced_fields = ['waypoint', 'waypoint_name', 'tips', 'recommended_gear', 'rewards']
    
    total_bosses = len(WORLD_BOSSES)
    enhanced_bosses = 0
    
    for boss_id, boss_data in WORLD_BOSSES.items():
        print(f"\n🔍 Testing: {boss_data['name']}")
        
        # Check required fields
        missing_required = [field for field in required_fields if field not in boss_data]
        if missing_required:
            print(f"  ❌ Missing required fields: {missing_required}")
        else:
            print(f"  ✅ All required fields present")
        
        # Check enhanced fields
        present_enhanced = [field for field in enhanced_fields if field in boss_data and boss_data[field]]
        if len(present_enhanced) >= 3:  # At least 3 enhanced fields
            enhanced_bosses += 1
            print(f"  ✅ Enhanced data: {present_enhanced}")
        else:
            print(f"  ⚠️  Limited enhanced data: {present_enhanced}")
        
        # Test waypoint format
        if boss_data.get('waypoint'):
            waypoint = boss_data['waypoint']
            if waypoint.startswith('[&') and waypoint.endswith(']'):
                print(f"  ✅ Valid waypoint format: {waypoint}")
            else:
                print(f"  ❌ Invalid waypoint format: {waypoint}")
        
        # Test tips format
        if boss_data.get('tips'):
            tips = boss_data['tips']
            if isinstance(tips, list) and len(tips) > 0:
                print(f"  ✅ Tips available: {len(tips)} tips")
            else:
                print(f"  ❌ Invalid tips format")
    
    print(f"\n📊 Summary:")
    print(f"  Total bosses: {total_bosses}")
    print(f"  Enhanced bosses: {enhanced_bosses}")
    print(f"  Enhancement rate: {(enhanced_bosses/total_bosses)*100:.1f}%")

def test_spawn_timing():
    """Test spawn timing calculations"""
    print("\n\n⏰ Testing Spawn Timing Calculations...")
    print("=" * 50)
    
    now = datetime.now(IRAN_TIMEZONE)
    print(f"Current time: {now.strftime('%Y-%m-%d %H:%M:%S Iran Time')}")
    
    upcoming_spawns = []
    
    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        time_until = get_time_until_spawn(boss_id)
        
        if next_spawn and time_until:
            upcoming_spawns.append({
                'name': boss_data['name'],
                'next_spawn': next_spawn,
                'time_until': time_until,
                'seconds_until': time_until.total_seconds()
            })
    
    # Sort by time until spawn
    upcoming_spawns.sort(key=lambda x: x['seconds_until'])
    
    print(f"\n🔮 Next 5 upcoming spawns:")
    for i, spawn in enumerate(upcoming_spawns[:5], 1):
        hours = int(spawn['time_until'].total_seconds() // 3600)
        minutes = int((spawn['time_until'].total_seconds() % 3600) // 60)
        time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"
        
        print(f"  {i}. {spawn['name']}")
        print(f"     Spawn time: {spawn['next_spawn'].strftime('%H:%M Iran Time')}")
        print(f"     Time until: {time_str}")

def test_new_bosses():
    """Test that new bosses were added successfully"""
    print("\n\n🆕 Testing New Boss Additions...")
    print("=" * 50)
    
    new_bosses = [
        'ley_line_anomaly',
        'drakkar', 
        'twisted_marionette',
        'death_branded_shatterer',
        'palawa_joko',
        'claw_of_jormag'
    ]
    
    for boss_id in new_bosses:
        if boss_id in WORLD_BOSSES:
            boss = WORLD_BOSSES[boss_id]
            print(f"  ✅ {boss['name']} - {boss['map']}")
            print(f"     Difficulty: {boss.get('difficulty', 'Unknown')}")
            print(f"     Waypoint: {boss.get('waypoint', 'Not set')}")
        else:
            print(f"  ❌ Missing: {boss_id}")

def test_alert_message_data():
    """Test that alert messages have all required data"""
    print("\n\n📢 Testing Alert Message Data...")
    print("=" * 50)
    
    alert_ready_bosses = 0
    
    for boss_id, boss_data in WORLD_BOSSES.items():
        has_waypoint = bool(boss_data.get('waypoint'))
        has_tips = bool(boss_data.get('tips'))
        has_gear = bool(boss_data.get('recommended_gear'))
        has_rewards = bool(boss_data.get('rewards'))
        
        alert_ready = has_waypoint and has_tips and has_gear and has_rewards
        
        if alert_ready:
            alert_ready_bosses += 1
            print(f"  ✅ {boss_data['name']} - Fully enhanced")
        else:
            missing = []
            if not has_waypoint: missing.append("waypoint")
            if not has_tips: missing.append("tips")
            if not has_gear: missing.append("gear")
            if not has_rewards: missing.append("rewards")
            print(f"  ⚠️  {boss_data['name']} - Missing: {', '.join(missing)}")
    
    total = len(WORLD_BOSSES)
    print(f"\n📊 Alert Enhancement Summary:")
    print(f"  Fully enhanced: {alert_ready_bosses}/{total} ({(alert_ready_bosses/total)*100:.1f}%)")

if __name__ == "__main__":
    print("🚀 Enhanced Discord Bot Alert System Test")
    print("=" * 60)
    
    try:
        test_enhanced_boss_data()
        test_spawn_timing()
        test_new_bosses()
        test_alert_message_data()
        
        print("\n\n🎉 All tests completed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
