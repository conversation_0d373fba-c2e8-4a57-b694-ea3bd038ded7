import discord
from discord.ext import commands
import asyncio
import aiohttp
from datetime import datetime, timedelta
import pytz
from typing import Dict, Optional, Any, List
import os
import signal
import sys
import random
import json
from dotenv import load_dotenv
from enum import Enum
import config
import traceback

# Load environment variables
load_dotenv()

# Initialize the bot with intents and command prefix
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(
    command_prefix=commands.when_mentioned_or('!'),
    intents=intents,
    help_command=None  # Disable default help command
)

# Constants
IRAN_TIMEZONE = pytz.timezone('Asia/Tehran')
GW2_API_BASE = "https://api.guildwars2.com/v2"
GW2_WORLD_BOSSES = f"{GW2_API_BASE}/worldbosses"
GW2_EVENTS = f"{GW2_API_BASE}/events"
GW2_MAPS = f"{GW2_API_BASE}/maps"
GW2_ITEMS = f"{GW2_API_BASE}/items"
GW2_COMMERCE_PRICES = f"{GW2_API_BASE}/commerce/prices"

# Cache for boss data and countdown settings
boss_cache = {}
active_countdowns = {}  # {message_id: {'boss_id': str, 'channel_id': int, 'next_spawn': datetime}}
last_fetch = None
CACHE_DURATION = 300  # 5 minutes in seconds
COUNTDOWN_UPDATE_INTERVAL = 30  # seconds between countdown updates

# Cache for item data
item_cache = {}  # {item_id: item_data}
item_name_cache = {}  # {item_name_lower: item_id}
item_cache_last_update = None
ITEM_CACHE_DURATION = 3600  # 1 hour in seconds

# API Configuration Constants
API_MAX_RETRIES = 3
API_CIRCUIT_BREAKER_THRESHOLD = 5
API_CIRCUIT_BREAKER_TIMEOUT = 300  # 5 minutes
API_HEALTH_CHECK_INTERVAL = 120    # 2 minutes
API_REQUEST_TIMEOUT = 10           # 10 seconds
CACHE_EXTENDED_DURATION = 1800     # 30 minutes during outages

# World Boss Data - Enhanced with waypoints, tips, and complete coverage
# All times are in UTC and converted to Iran timezone for display
WORLD_BOSSES = {
    # Major World Bosses (Every 3 hours)
    "tequatl_the_sunless": {
        "name": "Tequatl the Sunless",
        "map": "Sparkfly Fen",
        "schedule": [0, 3, 6, 9, 12, 15, 18, 21],  # Every 3 hours starting at 00:00 UTC
        "duration": 30,  # Event duration in minutes
        "emoji": "🐲",
        "difficulty": "Hard",
        "pre_event_time": 15,  # Pre-events start 15 minutes before
        "waypoint": "[&BNABAAA=]",
        "waypoint_name": "Splintered Coast Waypoint",
        "tips": [
            "🎯 **Positioning**: Stay on the platforms, avoid the poison pools",
            "⚡ **Turrets**: Use turrets to damage Tequatl when he's in the air",
            "🛡️ **Defense**: Bring condition removal for poison",
            "👥 **Coordination**: Follow commander tags for organized phases"
        ],
        "recommended_gear": "Berserker's gear with condition removal utilities",
        "rewards": "Tequatl's Hoard, Ascended weapon chance, Achievement progress"
    },
    "the_shatterer": {
        "name": "The Shatterer",
        "map": "Blazeridge Steppes",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🐉",
        "difficulty": "Medium",
        "pre_event_time": 5,
        "waypoint": "[&BM0CAAA=]",
        "waypoint_name": "Lowland Burns Waypoint",
        "tips": [
            "🎯 **Crystal Phase**: Destroy crystals quickly to prevent healing",
            "⚡ **Artillery**: Use siege weapons for maximum damage",
            "🛡️ **Positioning**: Stay spread out to avoid AoE attacks",
            "💨 **Movement**: Be ready to dodge the breath attack"
        ],
        "recommended_gear": "Power-based builds, ranged weapons preferred",
        "rewards": "Shatterer chest, rare materials, karma"
    },
    "triple_trouble": {
        "name": "Triple Trouble",
        "map": "Bloodtide Coast",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Same as Shatterer
        "duration": 20,
        "emoji": "🐍",
        "difficulty": "Hard",
        "pre_event_time": 10,
        "waypoint": "[&BKoBAAA=]",
        "waypoint_name": "Kiel's Outpost Waypoint",
        "tips": [
            "🎯 **Three Heads**: Amber (west), Crimson (east), Cobalt (south)",
            "⚡ **Coordination**: Requires organized squads for each head",
            "🛡️ **Reflects**: Bring projectile reflection for Cobalt",
            "💀 **Husks**: Kill husks quickly to prevent wurm healing"
        ],
        "recommended_gear": "Organized group required, follow commander instructions",
        "rewards": "Triple Trouble chest, rare drops, achievements"
    },
    "karka_queen": {
        "name": "Karka Queen",
        "map": "Southsun Cove",
        "schedule": [2, 6, 10, 14, 18, 22],  # Every 4 hours starting at 02:00 UTC
        "duration": 15,
        "emoji": "🦀",
        "difficulty": "Medium",
        "pre_event_time": 5,
        "waypoint": "[&BN4HAAA=]",
        "waypoint_name": "Kiel's Outpost Waypoint",
        "tips": [
            "🎯 **Weak Points**: Attack the soft underbelly when exposed",
            "⚡ **Rolling**: Dodge when she rolls across the beach",
            "🛡️ **Adds**: Clear smaller karkas quickly",
            "💨 **Positioning**: Stay mobile to avoid being trapped"
        ],
        "recommended_gear": "Power builds, condition damage also effective",
        "rewards": "Karka Queen chest, Southsun materials"
    },
    "claw_of_jormag": {
        "name": "Claw of Jormag",
        "map": "Frostgorge Sound",
        "schedule": [2, 5, 8, 11, 14, 17, 20, 23],  # Every 3 hours starting at 02:00 UTC
        "duration": 20,
        "emoji": "🧊",
        "difficulty": "Hard",
        "pre_event_time": 10,
        "waypoint": "[&BMIDAAA=]",
        "waypoint_name": "Earthshake Waypoint",
        "tips": [
            "🎯 **Ice Wall**: Destroy ice walls to progress the fight",
            "⚡ **Crystals**: Use crystals to damage Jormag when vulnerable",
            "🛡️ **Positioning**: Stay behind cover during ice attacks",
            "❄️ **Condition Removal**: Bring skills to remove chill and freeze"
        ],
        "recommended_gear": "Condition removal, stability, ranged damage",
        "rewards": "Claw of Jormag chest, icy materials, achievements"
    },

    # Regular World Bosses (Every 2 hours)
    "shadow_behemoth": {
        "name": "Shadow Behemoth",
        "map": "Queensdale",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "👹",
        "difficulty": "Easy",
        "pre_event_time": 5,
        "waypoint": "[&BAgAAAA=]",
        "waypoint_name": "Godslost Swamp Waypoint",
        "tips": [
            "🎯 **Portals**: Close shadow portals to prevent adds",
            "⚡ **Positioning**: Stay at range to avoid melee attacks",
            "🛡️ **Simple Fight**: Good for new players to learn",
            "💀 **Adds**: Focus on clearing shadow creatures"
        ],
        "recommended_gear": "Any gear works, great for beginners",
        "rewards": "Behemoth chest, basic materials"
    },
    "svanir_shaman_chief": {
        "name": "Svanir Shaman Chief",
        "map": "Wayfarer Foothills",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "❄️",
        "difficulty": "Easy",
        "pre_event_time": 5,
        "waypoint": "[&BNQAAAA=]",
        "waypoint_name": "Taigan Groves Waypoint",
        "tips": [
            "🎯 **Totems**: Destroy ice totems to weaken the shaman",
            "⚡ **Ice Attacks**: Dodge the ice spear attacks",
            "🛡️ **Positioning**: Stay mobile to avoid AoE",
            "❄️ **Simple Mechanics**: Straightforward fight"
        ],
        "recommended_gear": "Any build works, beginner-friendly",
        "rewards": "Shaman chest, basic crafting materials"
    },
    "fire_elemental": {
        "name": "Fire Elemental",
        "map": "Metrica Province",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "🔥",
        "difficulty": "Easy",
        "pre_event_time": 5,
        "waypoint": "[&BAsAAAA=]",
        "waypoint_name": "Thaumanova Reactor Waypoint",
        "tips": [
            "🎯 **Lava Font**: Avoid standing in lava pools",
            "⚡ **Cooling**: Use water buckets to cool the elemental",
            "🛡️ **Fire Resistance**: Helpful but not required",
            "💧 **Water Phase**: Participate in cooling mechanics"
        ],
        "recommended_gear": "Any gear, fire resistance utilities helpful",
        "rewards": "Elemental chest, crafting materials"
    },
    "the_frozen_maw": {
        "name": "The Frozen Maw",
        "map": "Wayfarer Foothills",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 15,
        "emoji": "🧊",
        "difficulty": "Easy",
        "pre_event_time": 5,
        "waypoint": "[&BEIAAAA=]",
        "waypoint_name": "Frozen Sweeps Waypoint",
        "tips": [
            "🎯 **Ice Crystals**: Destroy crystals to damage the maw",
            "⚡ **Positioning**: Stay at the edges of the arena",
            "🛡️ **Ice Attacks**: Simple dodge mechanics",
            "❄️ **Beginner Friendly**: Great for learning boss fights"
        ],
        "recommended_gear": "Any build suitable, very accessible",
        "rewards": "Frozen Maw chest, basic materials"
    },
    "jungle_wurm": {
        "name": "Jungle Wurm",
        "map": "Caledon Forest",
        "schedule": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23],  # Every 2 hours, offset by 1
        "duration": 15,
        "emoji": "🪱",
        "difficulty": "Easy",
        "pre_event_time": 5,
        "waypoint": "[&BEQAAAA=]",
        "waypoint_name": "Wychmire Swamp Waypoint",
        "tips": [
            "🎯 **Weak Spots**: Attack when the wurm emerges",
            "⚡ **Positioning**: Stay spread out around the area",
            "🛡️ **Simple Fight**: Very straightforward mechanics",
            "🌿 **Nature Theme**: Enjoy the forest setting"
        ],
        "recommended_gear": "Any gear works, perfect for new players",
        "rewards": "Wurm chest, plant materials"
    },
    "great_jungle_wurm": {
        "name": "Great Jungle Wurm",
        "map": "Bloodtide Coast",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🐛",
        "difficulty": "Medium",
        "pre_event_time": 10,
        "waypoint": "[&BKoBAAA=]",
        "waypoint_name": "Kiel's Outpost Waypoint",
        "tips": [
            "🎯 **Phases**: Multiple phases with different mechanics",
            "⚡ **Positioning**: Follow the group for optimal damage",
            "🛡️ **Coordination**: Some organization helpful",
            "🌊 **Coastal Fight**: Scenic ocean backdrop"
        ],
        "recommended_gear": "Power builds recommended, group coordination helpful",
        "rewards": "Great Wurm chest, valuable materials"
    },

    # Other Bosses
    "admiral_taidha_covington": {
        "name": "Admiral Taidha Covington",
        "map": "Bloodtide Coast",
        "schedule": [0, 3, 6, 9, 12, 15, 18, 21],  # Every 3 hours with Tequatl
        "duration": 15,
        "emoji": "🏴‍☠️",
        "difficulty": "Medium",
        "pre_event_time": 10,
        "waypoint": "[&BKoBAAA=]",
        "waypoint_name": "Kiel's Outpost Waypoint",
        "tips": [
            "🎯 **Ship Combat**: Fight on and around pirate ships",
            "⚡ **Cannons**: Use ship cannons for extra damage",
            "🛡️ **Boarding**: Board the ship when possible",
            "🏴‍☠️ **Pirate Theme**: Enjoy the swashbuckling adventure"
        ],
        "recommended_gear": "Balanced builds, ranged weapons useful",
        "rewards": "Pirate chest, nautical materials"
    },
    "megadestroyer": {
        "name": "Megadestroyer",
        "map": "Mount Maelstrom",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 15,
        "emoji": "🤖",
        "difficulty": "Medium",
        "pre_event_time": 5,
        "waypoint": "[&BM4CAAA=]",
        "waypoint_name": "Maelstrom's Bile Waypoint",
        "tips": [
            "🎯 **Weak Points**: Target the glowing weak spots",
            "⚡ **Lava**: Avoid lava pools and fire attacks",
            "🛡️ **Positioning**: Stay mobile in the volcanic arena",
            "🔥 **Fire Resistance**: Helpful for survivability"
        ],
        "recommended_gear": "Fire resistance, power builds effective",
        "rewards": "Destroyer chest, volcanic materials"
    },
    "modniir_ulgoth": {
        "name": "Modniir Ulgoth",
        "map": "Harathi Hinterlands",
        "schedule": [2, 5, 8, 11, 14, 17, 20, 23],  # Every 3 hours starting at 02:00 UTC
        "duration": 15,
        "emoji": "🐂",
        "difficulty": "Medium",
        "pre_event_time": 5,
        "waypoint": "[&BLYCAAA=]",
        "waypoint_name": "The Bloodfields Waypoint",
        "tips": [
            "🎯 **Centaur Boss**: Large centaur with powerful attacks",
            "⚡ **Charge Attacks**: Dodge the charging attacks",
            "🛡️ **Positioning**: Stay at medium range",
            "🏹 **Ranged Combat**: Ranged weapons recommended"
        ],
        "recommended_gear": "Ranged builds, mobility skills helpful",
        "rewards": "Centaur chest, Harathi materials"
    },

    # Living World & Expansion Bosses
    "ley_line_anomaly": {
        "name": "Ley-Line Anomaly",
        "map": "Timberline Falls",  # Rotates between multiple maps
        "schedule": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23],  # Every 2 hours, offset
        "duration": 20,
        "emoji": "⚡",
        "difficulty": "Medium",
        "pre_event_time": 5,
        "waypoint": "[&BFkBAAA=]",
        "waypoint_name": "Thistlereed Grove Waypoint",
        "tips": [
            "🎯 **Rotating Location**: Spawns in different maps (check LFG)",
            "⚡ **Ley Energy**: Avoid standing in ley line attacks",
            "🛡️ **Positioning**: Stay mobile around the anomaly",
            "🔮 **Magic Damage**: Brings magic resistance if available"
        ],
        "recommended_gear": "Condition damage builds, magic resistance",
        "rewards": "Ley-Line Crystal, Unbound Magic, rare materials"
    },
    "drakkar": {
        "name": "Drakkar",
        "map": "Bjora Marches",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 25,
        "emoji": "🐉",
        "difficulty": "Hard",
        "pre_event_time": 15,
        "waypoint": "[&BAgMAAA=]",
        "waypoint_name": "Jora's Keep Waypoint",
        "tips": [
            "🎯 **Ice Prison**: Break allies out of ice prisons quickly",
            "⚡ **Phases**: Multiple phases with different mechanics",
            "🛡️ **Coordination**: Requires good team coordination",
            "❄️ **Condition Removal**: Essential for ice-based conditions"
        ],
        "recommended_gear": "Condition removal, stability, power builds",
        "rewards": "Drakkar chest, Eternal Ice Shards, rare drops"
    },
    "twisted_marionette": {
        "name": "Twisted Marionette",
        "map": "Kessex Hills",
        "schedule": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22],  # Every 2 hours
        "duration": 20,
        "emoji": "🎭",
        "difficulty": "Hard",
        "pre_event_time": 10,
        "waypoint": "[&BEIAAAA=]",
        "waypoint_name": "Cereboth Canyon Waypoint",
        "tips": [
            "🎯 **Platform Phases**: 5 platforms, each with different mechanics",
            "⚡ **Coordination**: Requires organized squads",
            "🛡️ **DPS Checks**: Strict time limits on platforms",
            "🎭 **Complex Fight**: Study mechanics beforehand"
        ],
        "recommended_gear": "High DPS builds, organized group required",
        "rewards": "Marionette chest, rare materials, achievements"
    },
    "death_branded_shatterer": {
        "name": "Death-Branded Shatterer",
        "map": "Jahai Bluffs",
        "schedule": [1, 4, 7, 10, 13, 16, 19, 22],  # Every 3 hours starting at 01:00 UTC
        "duration": 20,
        "emoji": "💀",
        "difficulty": "Hard",
        "pre_event_time": 10,
        "waypoint": "[&BPULAAA=]",
        "waypoint_name": "Sun's Refuge Waypoint",
        "tips": [
            "🎯 **Branded Mechanics**: Enhanced version of original Shatterer",
            "⚡ **Crystal Phases**: More complex crystal mechanics",
            "🛡️ **Death Magic**: Avoid death-branded attacks",
            "💀 **Higher Difficulty**: Requires more coordination"
        ],
        "recommended_gear": "High-level gear, condition removal, organized group",
        "rewards": "Death-Branded chest, Volatile Magic, rare drops"
    },
    "palawa_joko": {
        "name": "Palawa Joko",
        "map": "Domain of Vabbi",
        "schedule": [2, 6, 10, 14, 18, 22],  # Every 4 hours starting at 02:00 UTC
        "duration": 30,
        "emoji": "💀",
        "difficulty": "Hard",
        "pre_event_time": 15,
        "waypoint": "[&BKMLAAA=]",
        "waypoint_name": "Necropolis Waypoint",
        "tips": [
            "🎯 **Undead Lord**: Powerful necromancer with multiple phases",
            "⚡ **Minion Waves**: Clear undead minions quickly",
            "🛡️ **Death Magic**: Strong condition removal needed",
            "💀 **Epic Fight**: One of the most challenging bosses"
        ],
        "recommended_gear": "Condition removal, high DPS, organized group",
        "rewards": "Joko's chest, Elegy Mosaics, unique drops"
    }
}

# Alert tracking
sent_alerts = {}  # {boss_id: {'10min': timestamp, '5min': timestamp, 'spawn': timestamp}}

# API Status Enum
class APIStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    DOWN = "down"

class GW2APIManager:
    """Manages GW2 API requests with circuit breaker pattern and health monitoring"""

    def __init__(self):
        self.session = None
        self.status = APIStatus.HEALTHY
        self.failure_count = 0
        self.last_failure_time = None
        self.circuit_open = False

    async def _get_session(self):
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=API_REQUEST_TIMEOUT)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()

    async def _make_request(self, url: str, retries: int = API_MAX_RETRIES) -> Optional[Dict]:
        """Make HTTP request with retry logic"""
        session = await self._get_session()

        for attempt in range(retries + 1):
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        self._record_success()
                        return data
                    elif response.status == 429:  # Rate limited
                        wait_time = min(2 ** attempt, 60)
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        self._record_failure()
                        return None

            except asyncio.TimeoutError:
                self._record_failure()
                if attempt < retries:
                    await asyncio.sleep(2 ** attempt)
                continue
            except Exception as e:
                self._record_failure()
                print(f"API request error: {e}")
                if attempt < retries:
                    await asyncio.sleep(2 ** attempt)
                continue

        return None

    def _record_success(self):
        """Record successful API call"""
        self.failure_count = 0
        self.circuit_open = False
        if self.status != APIStatus.HEALTHY:
            self.status = APIStatus.HEALTHY
            print("✅ GW2 API status: HEALTHY")

    def _record_failure(self):
        """Record failed API call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now(IRAN_TIMEZONE)

        if self.failure_count >= API_CIRCUIT_BREAKER_THRESHOLD:
            self.circuit_open = True
            self.status = APIStatus.DOWN
            print(f"❌ GW2 API status: DOWN (Circuit breaker opened)")
        elif self.failure_count > 1:
            self.status = APIStatus.DEGRADED
            print(f"⚠️ GW2 API status: DEGRADED ({self.failure_count} failures)")

    def is_circuit_open(self) -> bool:
        """Check if circuit breaker is open"""
        if not self.circuit_open:
            return False

        # Check if enough time has passed to try again
        if self.last_failure_time:
            time_since_failure = (datetime.now(IRAN_TIMEZONE) - self.last_failure_time).total_seconds()
            if time_since_failure > API_CIRCUIT_BREAKER_TIMEOUT:
                self.circuit_open = False
                print("🔄 Circuit breaker reset - attempting API reconnection")
                return False

        return True

    async def get_world_bosses(self) -> Optional[List[Dict]]:
        """Fetch world boss data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(GW2_WORLD_BOSSES)

    async def get_events(self) -> Optional[List[Dict]]:
        """Fetch event data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(GW2_EVENTS)

    async def get_all_items(self) -> Optional[List[int]]:
        """Fetch all item IDs from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(GW2_ITEMS)

    async def get_item(self, item_id: int) -> Optional[Dict]:
        """Fetch specific item data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(f"{GW2_ITEMS}/{item_id}")

    async def get_items_bulk(self, item_ids: List[int]) -> Optional[List[Dict]]:
        """Fetch multiple items data from GW2 API"""
        if self.is_circuit_open():
            return None

        # API supports bulk requests with comma-separated IDs
        ids_str = ",".join(map(str, item_ids))
        return await self._make_request(f"{GW2_ITEMS}?ids={ids_str}")

    async def get_item_price(self, item_id: int) -> Optional[Dict]:
        """Fetch item price data from GW2 API"""
        if self.is_circuit_open():
            return None

        return await self._make_request(f"{GW2_COMMERCE_PRICES}/{item_id}")

    async def get_item_prices_bulk(self, item_ids: List[int]) -> Optional[List[Dict]]:
        """Fetch multiple item prices from GW2 API"""
        if self.is_circuit_open():
            return None

        ids_str = ",".join(map(str, item_ids))
        return await self._make_request(f"{GW2_COMMERCE_PRICES}?ids={ids_str}")

    def get_status(self) -> Dict[str, Any]:
        """Get current API status information"""
        return {
            "status": self.status.value,
            "failure_count": self.failure_count,
            "circuit_open": self.circuit_open,
            "last_failure": self.last_failure_time.isoformat() if self.last_failure_time else None
        }

# Helper functions for boss timing
def get_next_spawn_time(boss_id: str) -> Optional[datetime]:
    """Calculate the next spawn time for a boss"""
    if boss_id not in WORLD_BOSSES:
        return None

    boss = WORLD_BOSSES[boss_id]

    # Work with UTC time for calculations, then convert to Iran time for display
    utc_now = datetime.utcnow()

    # Find next spawn time in UTC
    for hour in boss["schedule"]:
        spawn_time_utc = utc_now.replace(hour=hour, minute=0, second=0, microsecond=0)

        # If this spawn time hasn't passed today (in UTC)
        if spawn_time_utc > utc_now:
            # Convert to Iran timezone for return
            spawn_time_iran = spawn_time_utc.replace(tzinfo=pytz.UTC).astimezone(IRAN_TIMEZONE)
            return spawn_time_iran

    # If no spawn time today, get first spawn time tomorrow (in UTC)
    tomorrow_utc = utc_now + timedelta(days=1)
    first_hour = min(boss["schedule"])
    spawn_time_utc = tomorrow_utc.replace(hour=first_hour, minute=0, second=0, microsecond=0)

    # Convert to Iran timezone for return
    spawn_time_iran = spawn_time_utc.replace(tzinfo=pytz.UTC).astimezone(IRAN_TIMEZONE)
    return spawn_time_iran

def get_time_until_spawn(boss_id: str) -> Optional[timedelta]:
    """Get time remaining until next spawn"""
    next_spawn = get_next_spawn_time(boss_id)
    if next_spawn:
        return next_spawn - datetime.now(IRAN_TIMEZONE)
    return None

# Item helper functions
async def update_item_cache():
    """Update the item cache with all items from the API"""
    global item_cache, item_name_cache, item_cache_last_update

    try:
        print("🔄 Updating item cache...")

        # Get all item IDs
        all_item_ids = await api_manager.get_all_items()
        if not all_item_ids:
            print("❌ Failed to fetch item IDs")
            return False

        print(f"📊 Found {len(all_item_ids)} items, fetching data...")

        # Fetch items in batches of 200 (API limit)
        batch_size = 200
        all_items = []

        for i in range(0, len(all_item_ids), batch_size):
            batch_ids = all_item_ids[i:i + batch_size]
            batch_items = await api_manager.get_items_bulk(batch_ids)

            if batch_items:
                all_items.extend(batch_items)
                print(f"📦 Fetched batch {i//batch_size + 1}/{(len(all_item_ids) + batch_size - 1)//batch_size}")
            else:
                print(f"❌ Failed to fetch batch {i//batch_size + 1}")

        # Update caches
        item_cache.clear()
        item_name_cache.clear()

        for item in all_items:
            item_id = item['id']
            item_cache[item_id] = item

            # Create searchable name entries
            item_name = item['name'].lower()
            item_name_cache[item_name] = item_id

            # Also add partial matches for better search
            words = item_name.split()
            for word in words:
                if len(word) >= 3:  # Only index words with 3+ characters
                    if word not in item_name_cache:
                        item_name_cache[word] = item_id

        item_cache_last_update = datetime.now(IRAN_TIMEZONE)
        print(f"✅ Item cache updated with {len(all_items)} items")
        return True

    except Exception as e:
        print(f"❌ Error updating item cache: {e}")
        return False

async def search_item_by_name(item_name: str) -> Optional[Dict]:
    """Search for an item by name"""
    global item_cache, item_name_cache, item_cache_last_update

    # Check if cache needs updating
    if (item_cache_last_update is None or
        (datetime.now(IRAN_TIMEZONE) - item_cache_last_update).total_seconds() > ITEM_CACHE_DURATION):
        await update_item_cache()

    if not item_name_cache:
        print("❌ Item cache is empty")
        return None

    item_name_lower = item_name.lower().strip()

    # Exact match first
    if item_name_lower in item_name_cache:
        item_id = item_name_cache[item_name_lower]
        return item_cache.get(item_id)

    # Partial match - find items containing the search term
    matches = []
    for cached_name, item_id in item_name_cache.items():
        if item_name_lower in cached_name:
            item_data = item_cache.get(item_id)
            if item_data:
                matches.append(item_data)

    # Return the first match if any found
    if matches:
        # Sort by name length to prefer closer matches
        matches.sort(key=lambda x: len(x['name']))
        return matches[0]

    return None

def format_price(copper_value: int) -> str:
    """Format copper value into gold, silver, copper display"""
    if copper_value == 0:
        return "0c"

    gold = copper_value // 10000
    silver = (copper_value % 10000) // 100
    copper = copper_value % 100

    parts = []
    if gold > 0:
        parts.append(f"{gold}g")
    if silver > 0:
        parts.append(f"{silver}s")
    if copper > 0:
        parts.append(f"{copper}c")

    return " ".join(parts) if parts else "0c"

def get_rarity_color(rarity: str) -> int:
    """Get Discord embed color based on item rarity"""
    rarity_colors = {
        "Junk": 0x8B4513,      # Brown
        "Basic": 0x000000,      # Black
        "Fine": 0x62A4DA,       # Blue
        "Masterwork": 0x1a9306, # Green
        "Rare": 0xfcd00b,       # Yellow
        "Exotic": 0xffa405,     # Orange
        "Ascended": 0xe91e63,   # Pink
        "Legendary": 0x4c139d   # Purple
    }
    return rarity_colors.get(rarity, 0x808080)  # Default gray

async def send_alert_message(channel_id: int, boss_id: str, alert_type: str):
    """Send enhanced alert message to Discord channel with rich information"""
    try:
        channel = bot.get_channel(channel_id)
        if not channel:
            print(f"❌ Could not find channel {channel_id}")
            return

        boss = WORLD_BOSSES.get(boss_id)
        if not boss:
            print(f"❌ Unknown boss: {boss_id}")
            return

        next_spawn = get_next_spawn_time(boss_id)
        if not next_spawn:
            return

        # Enhanced embed with different styling based on alert type
        if alert_type == "10min":
            embed = discord.Embed(
                title=f"🔔 **10 MINUTE WARNING**",
                description=f"{boss['emoji']} **{boss['name']}** spawns in **10 minutes**!\n\n*Time to start heading to the location!*",
                color=0xffaa00
            )
        elif alert_type == "5min":
            embed = discord.Embed(
                title=f"⚠️ **5 MINUTE WARNING**",
                description=f"{boss['emoji']} **{boss['name']}** spawns in **5 minutes**!\n\n*Get ready, almost time!*",
                color=0xff6600
            )
        elif alert_type == "spawn":
            embed = discord.Embed(
                title=f"🚨 **SPAWNING NOW!** 🚨",
                description=f"# {boss['emoji']} **{boss['name']}** IS SPAWNING NOW!\n\n**🏃‍♂️ GO GO GO! The fight has begun!**",
                color=0xff0000
            )

        # Basic information fields
        embed.add_field(
            name="📍 Location",
            value=f"**{boss['map']}**",
            inline=True
        )

        embed.add_field(
            name="⏱️ Duration",
            value=f"**{boss['duration']} minutes**",
            inline=True
        )

        embed.add_field(
            name="🎯 Difficulty",
            value=f"**{boss.get('difficulty', 'Unknown')}**",
            inline=True
        )

        # Waypoint information with click-to-copy
        if boss.get('waypoint'):
            waypoint_text = f"**{boss.get('waypoint_name', 'Waypoint')}**\n`{boss['waypoint']}`\n*Click to copy waypoint code*"
            embed.add_field(
                name="🗺️ Waypoint (Click to Copy)",
                value=waypoint_text,
                inline=False
            )

        # Enhanced information for spawn alerts
        if alert_type == "spawn":
            # Fighting tips
            if boss.get('tips'):
                tips_text = "\n".join(boss['tips'][:3])  # Show first 3 tips
                embed.add_field(
                    name="⚔️ Fighting Tips",
                    value=tips_text,
                    inline=False
                )

            # Recommended gear
            if boss.get('recommended_gear'):
                embed.add_field(
                    name="🛡️ Recommended Gear",
                    value=f"*{boss['recommended_gear']}*",
                    inline=False
                )

            # Rewards
            if boss.get('rewards'):
                embed.add_field(
                    name="🎁 Rewards",
                    value=f"*{boss['rewards']}*",
                    inline=False
                )

        # Timing information
        spawn_time_str = next_spawn.strftime("%H:%M Iran Time")
        if alert_type == "spawn":
            embed.add_field(
                name="⏰ Event Time",
                value=f"**{spawn_time_str}** - **NOW ACTIVE!**",
                inline=False
            )
        else:
            embed.add_field(
                name="⏰ Spawn Time",
                value=f"**{spawn_time_str}**",
                inline=False
            )

        # Pre-event information for early warnings
        if alert_type in ["10min", "5min"] and boss.get('pre_event_time', 0) > 0:
            embed.add_field(
                name="⚡ Pre-Events",
                value=f"Start **{boss['pre_event_time']} minutes** before main boss",
                inline=False
            )

        # Footer with enhanced information
        embed.timestamp = datetime.now(IRAN_TIMEZONE)
        if alert_type == "spawn":
            embed.set_footer(
                text="🔥 ACTIVE NOW! Join the fight! • GW2 World Boss Timer",
                icon_url="https://wiki.guildwars2.com/images/thumb/d/d2/Guild_Wars_2_logo.png/150px-Guild_Wars_2_logo.png"
            )
        else:
            embed.set_footer(text="GW2 World Boss Timer • Get ready!")

        # Send the enhanced message
        await channel.send(embed=embed)

        # For spawn alerts, also send the waypoint as a separate message for easy copying
        if alert_type == "spawn" and boss.get('waypoint'):
            waypoint_msg = f"📍 **Quick Waypoint Copy**: {boss['waypoint']}"
            await channel.send(waypoint_msg)

        print(f"✅ Sent enhanced {alert_type} alert for {boss['name']}")

    except Exception as e:
        print(f"❌ Error sending alert: {e}")
        traceback.print_exc()

async def boss_timer_loop():
    """Main background task that monitors boss spawns and sends alerts"""
    print("🔄 Starting boss timer loop...")

    while True:
        try:
            await asyncio.sleep(30)  # Check every 30 seconds

            if not bot.is_ready():
                continue

            now = datetime.now(IRAN_TIMEZONE)
            print(f"🕐 Timer check at {now.strftime('%H:%M:%S Iran Time')}")

            alerts_sent_this_cycle = 0

            for boss_id, boss_data in WORLD_BOSSES.items():
                next_spawn = get_next_spawn_time(boss_id)
                if not next_spawn:
                    continue

                # Calculate time until spawn (both times are in Iran timezone now)
                time_until = (next_spawn - now).total_seconds()

                # Initialize alert tracking for this boss if needed
                if boss_id not in sent_alerts:
                    sent_alerts[boss_id] = {}

                # Debug logging for upcoming spawns
                if time_until <= 1800:  # Log if within 30 minutes
                    minutes_until = int(time_until // 60)
                    print(f"📊 {boss_data['name']}: {minutes_until}m until spawn ({next_spawn.strftime('%H:%M Iran Time')})")

                # 10 minute warning (between 10:00 and 9:30 before spawn)
                if 570 <= time_until <= 600:  # 9.5 to 10 minutes
                    alert_key = f"10min-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        print(f"🔔 Sending 10min alert for {boss_data['name']}")
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "10min")
                        sent_alerts[boss_id][alert_key] = now
                        alerts_sent_this_cycle += 1

                # 5 minute warning (between 5:00 and 4:30 before spawn)
                elif 270 <= time_until <= 300:  # 4.5 to 5 minutes
                    alert_key = f"5min-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        print(f"⚠️ Sending 5min alert for {boss_data['name']}")
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "5min")
                        sent_alerts[boss_id][alert_key] = now
                        alerts_sent_this_cycle += 1

                # Spawn notification (between 0 and 30 seconds before spawn)
                elif 0 <= time_until <= 30:
                    alert_key = f"spawn-{next_spawn.strftime('%Y-%m-%d-%H-%M')}"
                    if alert_key not in sent_alerts[boss_id]:
                        print(f"🚨 Sending spawn alert for {boss_data['name']}")
                        await send_alert_message(config.TARGET_CHANNEL_ID, boss_id, "spawn")
                        sent_alerts[boss_id][alert_key] = now
                        alerts_sent_this_cycle += 1

            if alerts_sent_this_cycle > 0:
                print(f"✅ Sent {alerts_sent_this_cycle} alerts this cycle")

            # Clean up old alert records (older than 24 hours)
            cutoff_time = now - timedelta(hours=24)
            cleaned_count = 0
            for boss_id in list(sent_alerts.keys()):
                for alert_key in list(sent_alerts[boss_id].keys()):
                    if sent_alerts[boss_id][alert_key] < cutoff_time:
                        del sent_alerts[boss_id][alert_key]
                        cleaned_count += 1

                # Remove empty boss entries
                if not sent_alerts[boss_id]:
                    del sent_alerts[boss_id]

            if cleaned_count > 0:
                print(f"🧹 Cleaned up {cleaned_count} old alert records")

        except Exception as e:
            print(f"❌ Error in boss timer loop: {e}")
            traceback.print_exc()
            await asyncio.sleep(60)  # Wait longer on error

# Global API manager instance
api_manager = None
timer_task = None  # Track the timer task

# Changelog function
async def send_changelog_notification(title: str, description: str, changes: List[str], version: str = None):
    """Send changelog notification to the changelog channel"""
    try:
        changelog_channel = bot.get_channel(config.CHANGELOG_CHANNEL_ID)
        if not changelog_channel:
            print(f"❌ Could not find changelog channel {config.CHANGELOG_CHANNEL_ID}")
            return

        embed = discord.Embed(
            title=f"📋 {title}",
            description=description,
            color=0x00ff00
        )

        if version:
            embed.add_field(
                name="🏷️ Version",
                value=f"`{version}`",
                inline=True
            )

        embed.add_field(
            name="📅 Date",
            value=datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M Iran Time"),
            inline=True
        )

        # Add changes
        if changes:
            changes_text = "\n".join([f"• {change}" for change in changes])
            embed.add_field(
                name="🆕 Changes",
                value=changes_text,
                inline=False
            )

        embed.timestamp = datetime.now(IRAN_TIMEZONE)
        embed.set_footer(text="GW2 Bot Changelog")

        await changelog_channel.send(embed=embed)
        print(f"✅ Sent changelog notification to channel {config.CHANGELOG_CHANNEL_ID}")

    except Exception as e:
        print(f"❌ Error sending changelog notification: {e}")

# Slash Commands
@bot.slash_command(
    name="help",
    description="Show available commands",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def help_command(ctx):
    """Display help information"""
    embed = discord.Embed(
        title="🎮 Guild Wars 2 World Boss Bot",
        description="Track world boss spawns and get notifications!",
        color=0x00ff00
    )

    embed.add_field(
        name="📋 World Boss Commands",
        value=(
            "`/help` - Show this help message\n"
            "`/next_spawns` - Show next 5 bosses spawning soon\n"
            "`/boss_list` - List all world bosses\n"
            "`/next_boss [boss_name]` - Get detailed info for a specific boss\n"
            "`/timer_status` - Check timer loop status and upcoming alerts\n"
            "`/validate_data` - Validate bot data accuracy\n"
            "`/api_status` - Check GW2 API connection status"
        ),
        inline=False
    )

    embed.add_field(
        name="🛒 Item & Trading Commands",
        value=(
            "`/item [item_name]` - Get detailed item information\n"
            "`/price [item_name]` - Get detailed price and trading information\n"
            "*Both commands support autocomplete for item names*"
        ),
        inline=False
    )

    embed.add_field(
        name="ℹ️ Information Commands",
        value=(
            "`/changelog` - View recent bot updates and changes\n"
            "`/help` - Show this help message"
        ),
        inline=False
    )

    embed.add_field(
        name="🔔 Enhanced Auto Alerts",
        value=(
            "The bot automatically sends rich alerts:\n"
            "• **10 minutes before spawn** - Preparation warning\n"
            "• **5 minutes before spawn** - Get ready alert\n"
            "• **When boss spawns** - Enhanced spawn notification with:\n"
            "  └ 🗺️ Click-to-copy waypoint codes\n"
            "  └ ⚔️ Fighting tips and strategies\n"
            "  └ 🛡️ Recommended gear suggestions\n"
            "  └ 🎁 Reward information"
        ),
        inline=False
    )

    embed.add_field(
        name="ℹ️ Info",
        value=f"Configured for channel: <#{config.TARGET_CHANNEL_ID}>",
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="GW2 World Boss Timer")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="boss_list",
    description="List all world bosses and their next spawn times",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def boss_list(ctx):
    """Display list of all world bosses"""
    embed = discord.Embed(
        title="🐉 World Boss Schedule",
        description="Next spawn times for all world bosses",
        color=0x0099ff
    )

    boss_times = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until:
                hours = int(time_until.total_seconds() // 3600)
                minutes = int((time_until.total_seconds() % 3600) // 60)
                time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

                boss_times.append({
                    'name': boss_data['name'],
                    'emoji': boss_data['emoji'],
                    'map': boss_data['map'],
                    'spawn_time': next_spawn.strftime("%H:%M"),
                    'time_until': time_str,
                    'seconds_until': time_until.total_seconds()
                })

    # Sort by time until spawn
    boss_times.sort(key=lambda x: x['seconds_until'])

    # Split into chunks for multiple fields
    chunk_size = 8
    for i in range(0, len(boss_times), chunk_size):
        chunk = boss_times[i:i + chunk_size]
        field_value = ""

        for boss in chunk:
            field_value += f"{boss['emoji']} **{boss['name']}**\n"
            field_value += f"📍 {boss['map']} • ⏰ {boss['spawn_time']} Iran Time • ⏳ {boss['time_until']}\n\n"

        field_name = "🕐 Next Spawns" if i == 0 else "🕐 More Spawns"
        embed.add_field(name=field_name, value=field_value, inline=False)

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Times shown in Iran Time (UTC+3:30)")

    await ctx.respond(embed=embed)

async def boss_autocomplete(ctx: discord.AutocompleteContext):
    """Autocomplete function for boss names"""
    user_input = ctx.value.lower() if ctx.value else ""

    # Create list of boss choices with emoji and name
    choices = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        boss_display_name = f"{boss_data['emoji']} {boss_data['name']}"

        # If user typed something, filter by it
        if user_input:
            if (user_input in boss_data['name'].lower() or
                user_input in boss_id.lower() or
                user_input in boss_data['map'].lower()):
                choices.append(boss_display_name)
        else:
            choices.append(boss_display_name)

    # Sort choices alphabetically and limit to 25 (Discord limit)
    choices.sort()
    return choices[:25]

async def item_autocomplete(ctx: discord.AutocompleteContext):
    """Autocomplete function for item names"""
    user_input = ctx.value.lower() if ctx.value else ""

    if not user_input or len(user_input) < 2:
        # Return some popular items if no input
        popular_items = [
            "Mystic Coin", "Ectoplasm", "Lodestone", "Precursor", "Legendary",
            "Ascended", "Exotic", "Rare", "Masterwork", "Fine"
        ]
        return popular_items[:25]

    # Search through cached item names
    choices = []
    if item_name_cache:
        for item_name, item_id in item_name_cache.items():
            if user_input in item_name and len(item_name) > len(user_input):
                item_data = item_cache.get(item_id)
                if item_data:
                    choices.append(item_data['name'])
                    if len(choices) >= 25:
                        break

    # If no cached results and input is substantial, suggest the input itself
    if not choices and len(user_input) >= 3:
        choices.append(user_input.title())

    return choices[:25]

@bot.slash_command(
    name="next_boss",
    description="Get next spawn time for a specific boss",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def next_boss(ctx, boss_name: discord.Option(str, "Choose a world boss", autocomplete=boss_autocomplete)):
    """Get next spawn time for a specific boss"""
    # Extract the actual boss name from the display format "emoji name"
    # Remove emoji and extra spaces
    clean_boss_name = boss_name
    if " " in boss_name:
        # Remove the emoji part (first part before space)
        parts = boss_name.split(" ", 1)
        if len(parts) > 1:
            clean_boss_name = parts[1]

    # Find boss by name (case insensitive, partial match)
    boss_name_lower = clean_boss_name.lower()
    found_boss = None
    found_boss_id = None

    for boss_id, boss_data in WORLD_BOSSES.items():
        if (boss_name_lower in boss_data['name'].lower() or
            boss_name_lower in boss_id.lower() or
            boss_data['name'].lower() == boss_name_lower):
            found_boss = boss_data
            found_boss_id = boss_id
            break

    if not found_boss:
        embed = discord.Embed(
            title="❌ Boss Not Found",
            description=f"Could not find a boss matching '{clean_boss_name}'",
            color=0xff0000
        )
        embed.add_field(
            name="💡 Tip",
            value="Use `/boss_list` to see all available bosses",
            inline=False
        )
        await ctx.respond(embed=embed)
        return

    next_spawn = get_next_spawn_time(found_boss_id)
    time_until = get_time_until_spawn(found_boss_id)

    if not next_spawn or not time_until:
        embed = discord.Embed(
            title="❌ Error",
            description="Could not calculate spawn time",
            color=0xff0000
        )
        await ctx.respond(embed=embed)
        return

    embed = discord.Embed(
        title=f"{found_boss['emoji']} {found_boss['name']}",
        color=0x00ff00
    )

    hours = int(time_until.total_seconds() // 3600)
    minutes = int((time_until.total_seconds() % 3600) // 60)
    time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

    embed.add_field(name="📍 Location", value=f"**{found_boss['map']}**", inline=True)
    embed.add_field(name="⏰ Next Spawn", value=f"**{next_spawn.strftime('%H:%M Iran Time')}**", inline=True)
    embed.add_field(name="⏳ Time Until", value=f"**{time_str}**", inline=True)
    embed.add_field(name="⏱️ Duration", value=f"**{found_boss['duration']} minutes**", inline=True)
    embed.add_field(name="🎯 Difficulty", value=f"**{found_boss.get('difficulty', 'Unknown')}**", inline=True)

    # Pre-event information
    pre_event_time = found_boss.get('pre_event_time', 0)
    if pre_event_time > 0:
        embed.add_field(name="⚡ Pre-events", value=f"**{pre_event_time} min** before main boss", inline=True)

    # Waypoint information
    if found_boss.get('waypoint'):
        waypoint_text = f"**{found_boss.get('waypoint_name', 'Waypoint')}**\n`{found_boss['waypoint']}`"
        embed.add_field(name="🗺️ Waypoint", value=waypoint_text, inline=False)

    # Fighting tips
    if found_boss.get('tips'):
        tips_text = "\n".join(found_boss['tips'][:4])  # Show first 4 tips
        embed.add_field(name="⚔️ Fighting Tips", value=tips_text, inline=False)

    # Recommended gear
    if found_boss.get('recommended_gear'):
        embed.add_field(name="🛡️ Recommended Gear", value=f"*{found_boss['recommended_gear']}*", inline=False)

    # Rewards
    if found_boss.get('rewards'):
        embed.add_field(name="🎁 Rewards", value=f"*{found_boss['rewards']}*", inline=False)

    # Show schedule
    schedule_str = ", ".join([f"{hour:02d}:00" for hour in found_boss['schedule']])
    embed.add_field(name="📅 Daily Schedule (UTC)", value=f"`{schedule_str}`", inline=False)

    # Add note about timezone
    embed.add_field(
        name="🌍 Timezone Info",
        value="*Schedule shows UTC times. Display times are converted to Iran Time (UTC+3:30)*",
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Times shown in Iran Time (UTC+3:30)")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="item",
    description="Get detailed information about a Guild Wars 2 item",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def item_command(ctx, item_name: discord.Option(str, "Enter item name", autocomplete=item_autocomplete)):
    """Get detailed information about a specific item"""
    await ctx.defer()  # This command might take a moment

    try:
        # Search for the item
        item_data = await search_item_by_name(item_name)

        if not item_data:
            embed = discord.Embed(
                title="❌ Item Not Found",
                description=f"Could not find an item matching '{item_name}'",
                color=0xff0000
            )
            embed.add_field(
                name="💡 Tips",
                value="• Try using the autocomplete suggestions\n• Check your spelling\n• Use partial names (e.g., 'mystic' for 'Mystic Coin')",
                inline=False
            )
            await ctx.followup.send(embed=embed)
            return

        # Get price data if item is tradeable
        price_data = None
        if not any(flag in item_data.get('flags', []) for flag in ['AccountBound', 'SoulbindOnAcquire', 'NoSell']):
            price_data = await api_manager.get_item_price(item_data['id'])

        # Create rich embed
        rarity_color = get_rarity_color(item_data.get('rarity', 'Basic'))
        embed = discord.Embed(
            title=f"{item_data['name']}",
            description=item_data.get('description', 'No description available'),
            color=rarity_color
        )

        # Set item icon
        if item_data.get('icon'):
            embed.set_thumbnail(url=item_data['icon'])

        # Basic item info
        embed.add_field(
            name="📊 Basic Info",
            value=(
                f"**Type**: {item_data.get('type', 'Unknown')}\n"
                f"**Rarity**: {item_data.get('rarity', 'Unknown')}\n"
                f"**Level**: {item_data.get('level', 0)}\n"
                f"**ID**: {item_data['id']}"
            ),
            inline=True
        )

        # Vendor value and chat link
        vendor_value = item_data.get('vendor_value', 0)
        chat_link = item_data.get('chat_link', 'N/A')

        embed.add_field(
            name="💰 Value & Link",
            value=(
                f"**Vendor Value**: {format_price(vendor_value)}\n"
                f"**Chat Link**: `{chat_link}`\n"
                f"*Click to copy chat link*"
            ),
            inline=True
        )

        # Trading Post prices
        if price_data and not price_data.get('whitelisted', True):
            buy_price = price_data.get('buys', {}).get('unit_price', 0)
            sell_price = price_data.get('sells', {}).get('unit_price', 0)
            buy_quantity = price_data.get('buys', {}).get('quantity', 0)
            sell_quantity = price_data.get('sells', {}).get('quantity', 0)

            embed.add_field(
                name="🏪 Trading Post",
                value=(
                    f"**Buy Orders**: {format_price(buy_price)} ({buy_quantity:,} available)\n"
                    f"**Sell Orders**: {format_price(sell_price)} ({sell_quantity:,} available)\n"
                    f"**Spread**: {format_price(sell_price - buy_price)}"
                ),
                inline=False
            )
        elif any(flag in item_data.get('flags', []) for flag in ['AccountBound', 'SoulbindOnAcquire', 'NoSell']):
            embed.add_field(
                name="🚫 Trading Post",
                value="This item cannot be traded",
                inline=False
            )
        else:
            embed.add_field(
                name="🏪 Trading Post",
                value="Price data unavailable",
                inline=False
            )

        # Item flags and restrictions
        flags = item_data.get('flags', [])
        restrictions = item_data.get('restrictions', [])

        if flags or restrictions:
            flag_text = ""
            if flags:
                flag_text += f"**Flags**: {', '.join(flags)}\n"
            if restrictions:
                flag_text += f"**Restrictions**: {', '.join(restrictions)}"

            embed.add_field(
                name="⚠️ Flags & Restrictions",
                value=flag_text,
                inline=False
            )

        # Game types
        game_types = item_data.get('game_types', [])
        if game_types:
            embed.add_field(
                name="🎮 Available In",
                value=", ".join(game_types),
                inline=True
            )

        # Item details (type-specific info)
        details = item_data.get('details', {})
        if details:
            detail_text = ""
            for key, value in details.items():
                if key != 'type' and value:  # Skip type as it's already shown
                    if isinstance(value, (int, float)):
                        detail_text += f"**{key.replace('_', ' ').title()}**: {value}\n"
                    elif isinstance(value, str):
                        detail_text += f"**{key.replace('_', ' ').title()}**: {value}\n"

            if detail_text:
                embed.add_field(
                    name="🔍 Details",
                    value=detail_text,
                    inline=False
                )

        embed.timestamp = datetime.now(IRAN_TIMEZONE)
        embed.set_footer(text="GW2 Item Database • Use /price for detailed trading info")

        await ctx.followup.send(embed=embed)

    except Exception as e:
        print(f"❌ Error in item command: {e}")
        traceback.print_exc()

        error_embed = discord.Embed(
            title="❌ Error",
            description="An error occurred while fetching item information. Please try again.",
            color=0xff0000
        )
        await ctx.followup.send(embed=error_embed)

@bot.slash_command(
    name="next_spawns",
    description="Show the next 5 world bosses spawning soon",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def next_spawns(ctx):
    """Show the next few world bosses spawning soon"""
    boss_times = []
    now = datetime.now(IRAN_TIMEZONE)

    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until:
                boss_times.append({
                    'name': boss_data['name'],
                    'emoji': boss_data['emoji'],
                    'map': boss_data['map'],
                    'spawn_time': next_spawn.strftime("%H:%M"),
                    'time_until_seconds': time_until.total_seconds(),
                    'time_until': time_until
                })

    # Sort by time until spawn and take first 5
    boss_times.sort(key=lambda x: x['time_until_seconds'])
    next_5_bosses = boss_times[:5]

    embed = discord.Embed(
        title="⏰ Next 5 World Boss Spawns",
        description="Upcoming world boss spawns in chronological order",
        color=0x0099ff
    )

    for i, boss in enumerate(next_5_bosses, 1):
        hours = int(boss['time_until'].total_seconds() // 3600)
        minutes = int((boss['time_until'].total_seconds() % 3600) // 60)
        time_str = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"

        # Add urgency indicators
        if boss['time_until_seconds'] <= 300:  # 5 minutes
            urgency = "🚨 SPAWNING SOON!"
        elif boss['time_until_seconds'] <= 600:  # 10 minutes
            urgency = "⚠️ Warning!"
        else:
            urgency = "🕐 Upcoming"

        embed.add_field(
            name=f"{i}. {boss['emoji']} {boss['name']} {urgency}",
            value=f"📍 **{boss['map']}**\n⏰ Spawns at **{boss['spawn_time']} Iran Time**\n⏳ In **{time_str}**",
            inline=False
        )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Use /next_boss to get detailed info about a specific boss")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="price",
    description="Get detailed price information for a Guild Wars 2 item",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def price_command(ctx, item_name: discord.Option(str, "Enter item name", autocomplete=item_autocomplete)):
    """Get detailed price information for a specific item"""
    await ctx.defer()  # This command might take a moment

    try:
        # Search for the item
        item_data = await search_item_by_name(item_name)

        if not item_data:
            embed = discord.Embed(
                title="❌ Item Not Found",
                description=f"Could not find an item matching '{item_name}'",
                color=0xff0000
            )
            embed.add_field(
                name="💡 Tips",
                value="• Try using the autocomplete suggestions\n• Check your spelling\n• Use partial names (e.g., 'mystic' for 'Mystic Coin')",
                inline=False
            )
            await ctx.followup.send(embed=embed)
            return

        # Check if item is tradeable
        if any(flag in item_data.get('flags', []) for flag in ['AccountBound', 'SoulbindOnAcquire', 'NoSell']):
            embed = discord.Embed(
                title="🚫 Item Not Tradeable",
                description=f"**{item_data['name']}** cannot be traded on the Trading Post",
                color=0xff6600
            )

            # Show vendor value if available
            vendor_value = item_data.get('vendor_value', 0)
            if vendor_value > 0:
                embed.add_field(
                    name="💰 Vendor Value",
                    value=format_price(vendor_value),
                    inline=True
                )

            # Show why it's not tradeable
            flags = item_data.get('flags', [])
            non_trade_flags = [flag for flag in flags if flag in ['AccountBound', 'SoulbindOnAcquire', 'NoSell']]
            if non_trade_flags:
                embed.add_field(
                    name="⚠️ Reason",
                    value=", ".join(non_trade_flags),
                    inline=True
                )

            await ctx.followup.send(embed=embed)
            return

        # Get price data
        price_data = await api_manager.get_item_price(item_data['id'])

        if not price_data:
            embed = discord.Embed(
                title="❌ Price Data Unavailable",
                description=f"Could not fetch price data for **{item_data['name']}**",
                color=0xff0000
            )
            embed.add_field(
                name="💡 Possible Reasons",
                value="• Item is not currently listed on Trading Post\n• API is temporarily unavailable\n• Item may not be tradeable",
                inline=False
            )
            await ctx.followup.send(embed=embed)
            return

        # Create rich price embed
        rarity_color = get_rarity_color(item_data.get('rarity', 'Basic'))
        embed = discord.Embed(
            title=f"💰 {item_data['name']} - Price Information",
            color=rarity_color
        )

        # Set item icon
        if item_data.get('icon'):
            embed.set_thumbnail(url=item_data['icon'])

        # Extract price information
        buy_orders = price_data.get('buys', {})
        sell_orders = price_data.get('sells', {})

        buy_price = buy_orders.get('unit_price', 0)
        sell_price = sell_orders.get('unit_price', 0)
        buy_quantity = buy_orders.get('quantity', 0)
        sell_quantity = sell_orders.get('quantity', 0)

        # Current prices
        embed.add_field(
            name="📈 Current Prices",
            value=(
                f"**Highest Buy Order**: {format_price(buy_price)}\n"
                f"**Lowest Sell Order**: {format_price(sell_price)}\n"
                f"**Spread**: {format_price(sell_price - buy_price)}"
            ),
            inline=True
        )

        # Order quantities
        embed.add_field(
            name="📊 Order Quantities",
            value=(
                f"**Buy Orders**: {buy_quantity:,} items\n"
                f"**Sell Orders**: {sell_quantity:,} items\n"
                f"**Total Volume**: {(buy_quantity + sell_quantity):,} items"
            ),
            inline=True
        )

        # Profit calculations
        if buy_price > 0 and sell_price > 0:
            # Calculate profit after 15% TP tax
            tp_tax = 0.15
            profit_per_item = int(buy_price * (1 - tp_tax)) - sell_price
            profit_percentage = (profit_per_item / sell_price * 100) if sell_price > 0 else 0

            embed.add_field(
                name="💸 Trading Profit",
                value=(
                    f"**Buy at**: {format_price(sell_price)}\n"
                    f"**Sell at**: {format_price(buy_price)}\n"
                    f"**Profit/Loss**: {format_price(profit_per_item)} ({profit_percentage:.1f}%)\n"
                    f"*After 15% TP tax*"
                ),
                inline=False
            )

        # Market status
        market_status = "🟢 Active"
        if buy_quantity == 0 and sell_quantity == 0:
            market_status = "🔴 No Orders"
        elif buy_quantity == 0:
            market_status = "🟡 No Buy Orders"
        elif sell_quantity == 0:
            market_status = "🟡 No Sell Orders"

        embed.add_field(
            name="📊 Market Status",
            value=market_status,
            inline=True
        )

        # Item rarity and type
        embed.add_field(
            name="🏷️ Item Info",
            value=(
                f"**Rarity**: {item_data.get('rarity', 'Unknown')}\n"
                f"**Type**: {item_data.get('type', 'Unknown')}\n"
                f"**Level**: {item_data.get('level', 0)}"
            ),
            inline=True
        )

        # Vendor value comparison
        vendor_value = item_data.get('vendor_value', 0)
        if vendor_value > 0:
            vendor_vs_tp = ""
            if sell_price > vendor_value:
                vendor_vs_tp = f"TP is {format_price(sell_price - vendor_value)} better"
            elif vendor_value > sell_price:
                vendor_vs_tp = f"Vendor is {format_price(vendor_value - sell_price)} better"
            else:
                vendor_vs_tp = "Same value"

            embed.add_field(
                name="🏪 Vendor Comparison",
                value=(
                    f"**Vendor Value**: {format_price(vendor_value)}\n"
                    f"**TP vs Vendor**: {vendor_vs_tp}"
                ),
                inline=False
            )

        # Add helpful notes
        embed.add_field(
            name="📝 Notes",
            value=(
                "• Prices update in real-time\n"
                "• Trading Post takes 15% tax on sales\n"
                "• Buy high, sell low for profit\n"
                "• Consider vendor value for minimum price"
            ),
            inline=False
        )

        embed.timestamp = datetime.now(IRAN_TIMEZONE)
        embed.set_footer(text="GW2 Trading Post Prices • Use /item for detailed item info")

        await ctx.followup.send(embed=embed)

    except Exception as e:
        print(f"❌ Error in price command: {e}")
        traceback.print_exc()

        error_embed = discord.Embed(
            title="❌ Error",
            description="An error occurred while fetching price information. Please try again.",
            color=0xff0000
        )
        await ctx.followup.send(embed=error_embed)

@bot.slash_command(
    name="test_alerts",
    description="Test the alert system (Admin only)",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def test_alerts(ctx):
    """Test the alert system by sending sample notifications"""
    # Simple admin check - you can modify this
    if ctx.author.id not in [378491140933943297]:  # Replace with your user ID
        await ctx.respond("❌ This command is for administrators only.", ephemeral=True)
        return

    embed = discord.Embed(
        title="🧪 Testing Alert System",
        description="Sending test notifications...",
        color=0xffaa00
    )

    await ctx.respond(embed=embed, ephemeral=True)

    # Test each alert type
    test_boss_id = "tequatl_the_sunless"

    try:
        # Send test alerts
        await send_alert_message(config.TARGET_CHANNEL_ID, test_boss_id, "10min")
        await asyncio.sleep(2)
        await send_alert_message(config.TARGET_CHANNEL_ID, test_boss_id, "5min")
        await asyncio.sleep(2)
        await send_alert_message(config.TARGET_CHANNEL_ID, test_boss_id, "spawn")

        # Send confirmation
        success_embed = discord.Embed(
            title="✅ Alert Test Complete",
            description="Test alerts have been sent to the monitoring channel.",
            color=0x00ff00
        )
        await ctx.followup.send(embed=success_embed, ephemeral=True)

    except Exception as e:
        error_embed = discord.Embed(
            title="❌ Alert Test Failed",
            description=f"Error: {str(e)}",
            color=0xff0000
        )
        await ctx.followup.send(embed=error_embed, ephemeral=True)

@bot.slash_command(
    name="timer_status",
    description="Check timer loop status and next alerts",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def timer_status(ctx):
    """Check the status of the timer loop and upcoming alerts"""
    embed = discord.Embed(
        title="⏰ Timer Status",
        description="Current status of the boss timer system",
        color=0x0099ff
    )

    # Check if timer loop is running (we'll add a global flag for this)
    embed.add_field(
        name="🔄 Timer Loop",
        value="Running" if bot.is_ready() else "Not Ready",
        inline=True
    )

    # Show next few alerts
    now = datetime.now(IRAN_TIMEZONE)
    upcoming_alerts = []

    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = (next_spawn - now).total_seconds()

            # Check if any alerts are coming up in the next 2 hours
            if 0 <= time_until <= 7200:  # Next 2 hours
                upcoming_alerts.append({
                    'boss': boss_data['name'],
                    'spawn_time': next_spawn,
                    'time_until': time_until,
                    'alerts': []
                })

                # Check which alerts will trigger
                if 570 <= time_until <= 600:
                    upcoming_alerts[-1]['alerts'].append("10min alert ready")
                elif 270 <= time_until <= 300:
                    upcoming_alerts[-1]['alerts'].append("5min alert ready")
                elif 0 <= time_until <= 30:
                    upcoming_alerts[-1]['alerts'].append("spawn alert ready")
                else:
                    # Calculate when next alert will be
                    if time_until > 600:
                        next_alert_in = time_until - 600
                        upcoming_alerts[-1]['alerts'].append(f"10min alert in {int(next_alert_in//60)}m")
                    elif time_until > 300:
                        next_alert_in = time_until - 300
                        upcoming_alerts[-1]['alerts'].append(f"5min alert in {int(next_alert_in//60)}m")
                    elif time_until > 30:
                        next_alert_in = time_until - 30
                        upcoming_alerts[-1]['alerts'].append(f"spawn alert in {int(next_alert_in//60)}m")

    # Sort by time until spawn
    upcoming_alerts.sort(key=lambda x: x['time_until'])

    if upcoming_alerts:
        alert_text = ""
        for alert in upcoming_alerts[:5]:  # Show next 5
            spawn_time = alert['spawn_time'].strftime('%H:%M Iran Time')
            minutes_until = int(alert['time_until'] // 60)
            alert_status = ", ".join(alert['alerts']) if alert['alerts'] else "No alerts pending"
            alert_text += f"• **{alert['boss']}**: {spawn_time} (in {minutes_until}m)\n  {alert_status}\n\n"

        embed.add_field(
            name="📋 Upcoming Alerts",
            value=alert_text,
            inline=False
        )
    else:
        embed.add_field(
            name="📋 Upcoming Alerts",
            value="No alerts in the next 2 hours",
            inline=False
        )

    # Show alert tracking info
    total_tracked = len(sent_alerts)
    embed.add_field(
        name="📊 Alert Tracking",
        value=f"Currently tracking {total_tracked} boss alert histories",
        inline=True
    )

    embed.add_field(
        name="🎯 Target Channel",
        value=f"<#{config.TARGET_CHANNEL_ID}>",
        inline=True
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Timer checks every 30 seconds")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="validate_data",
    description="Validate bot data against current time and known schedules",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def validate_data(ctx):
    """Validate the accuracy of our world boss data"""
    embed = discord.Embed(
        title="🔍 Data Validation Report",
        description="Checking accuracy of world boss schedules and timings",
        color=0x0099ff
    )

    now = datetime.now(IRAN_TIMEZONE)
    utc_now = datetime.utcnow()

    # Check timezone conversion
    embed.add_field(
        name="🌍 Timezone Check",
        value=f"Current Iran Time: {now.strftime('%H:%M:%S')}\nCurrent UTC: {utc_now.strftime('%H:%M:%S')}\nOffset: +3:30",
        inline=False
    )

    # Validate next few spawns
    upcoming_spawns = []
    for boss_id, boss_data in WORLD_BOSSES.items():
        next_spawn = get_next_spawn_time(boss_id)
        if next_spawn:
            time_until = get_time_until_spawn(boss_id)
            if time_until and time_until.total_seconds() <= 7200:  # Next 2 hours
                upcoming_spawns.append({
                    'name': boss_data['name'],
                    'spawn_time': next_spawn,
                    'time_until': time_until.total_seconds()
                })

    # Sort by spawn time
    upcoming_spawns.sort(key=lambda x: x['time_until'])

    if upcoming_spawns:
        validation_text = ""
        for spawn in upcoming_spawns[:5]:  # Show next 5
            minutes_until = int(spawn['time_until'] // 60)
            spawn_time_iran = spawn['spawn_time'].strftime('%H:%M Iran Time')
            validation_text += f"• {spawn['name']}: {spawn_time_iran} (in {minutes_until}m)\n"

        embed.add_field(
            name="⏰ Next Spawns (Validation)",
            value=validation_text,
            inline=False
        )

    # Data accuracy notes
    embed.add_field(
        name="✅ Data Sources",
        value=(
            "• Schedule based on official GW2 community timers\n"
            "• Times converted from UTC to Iran Time (+3:30)\n"
            "• Major bosses: Every 3 hours\n"
            "• Regular bosses: Every 2 hours\n"
            "• Pre-event times included where applicable"
        ),
        inline=False
    )

    embed.add_field(
        name="⚠️ Important Notes",
        value=(
            "• Times may vary by 1-2 minutes due to server lag\n"
            "• Pre-events must complete for boss to spawn\n"
            "• Some bosses require map population\n"
            "• Triple Trouble requires organized groups"
        ),
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="Data validated against official sources")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="api_status",
    description="Check GW2 API connection status",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def api_status(ctx):
    """Display GW2 API status"""
    global api_manager

    if not api_manager:
        embed = discord.Embed(
            title="❌ API Manager Not Initialized",
            color=0xff0000
        )
        await ctx.respond(embed=embed)
        return

    status_info = api_manager.get_status()

    # Determine color based on status
    color_map = {
        "healthy": 0x00ff00,
        "degraded": 0xffaa00,
        "down": 0xff0000
    }
    color = color_map.get(status_info["status"], 0x888888)

    # Status emoji
    emoji_map = {
        "healthy": "✅",
        "degraded": "⚠️",
        "down": "❌"
    }
    emoji = emoji_map.get(status_info["status"], "❓")

    embed = discord.Embed(
        title=f"{emoji} GW2 API Status",
        color=color
    )

    embed.add_field(
        name="Status",
        value=status_info["status"].upper(),
        inline=True
    )

    embed.add_field(
        name="Failure Count",
        value=str(status_info["failure_count"]),
        inline=True
    )

    embed.add_field(
        name="Circuit Breaker",
        value="OPEN" if status_info["circuit_open"] else "CLOSED",
        inline=True
    )

    if status_info["last_failure"]:
        last_failure = datetime.fromisoformat(status_info["last_failure"])
        embed.add_field(
            name="Last Failure",
            value=last_failure.strftime("%Y-%m-%d %H:%M:%S Iran Time"),
            inline=False
        )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="GW2 World Boss Timer")

    await ctx.respond(embed=embed)

@bot.slash_command(
    name="changelog",
    description="View recent bot updates and changes",
    guild_ids=[config.TARGET_GUILD_ID]
)
async def changelog_command(ctx):
    """Display recent changelog information"""
    embed = discord.Embed(
        title="📋 Recent Bot Updates",
        description="Latest changes and improvements to the GW2 Bot",
        color=0x00ff00
    )

    # Current version info
    embed.add_field(
        name="🏷️ Current Version",
        value="`v2.1.0` - Item & Price Commands Update",
        inline=False
    )

    # Latest changes
    embed.add_field(
        name="🆕 Latest Changes (v2.1.0)",
        value=(
            "• **New `/item [name]` command** - Get detailed item information\n"
            "• **New `/price [name]` command** - Get trading post prices & profit calculations\n"
            "• **Autocomplete support** - Both commands suggest item names as you type\n"
            "• **Rich embeds** - Color-coded by rarity with item icons\n"
            "• **Smart caching** - Fast search through 69,000+ items\n"
            "• **Enhanced help** - Updated to include new commands\n"
            "• **Command visibility** - Fixed all commands to appear properly\n"
            "• **Changelog system** - This command and automatic notifications"
        ),
        inline=False
    )

    # Previous version
    embed.add_field(
        name="📜 Previous Version (v2.0.0)",
        value=(
            "• Enhanced world boss alerts with multi-stage notifications\n"
            "• Rich embeds with waypoint codes and fighting tips\n"
            "• Comprehensive boss coverage with accurate timers\n"
            "• Iran Time timezone support\n"
            "• Robust error handling and API monitoring"
        ),
        inline=False
    )

    embed.add_field(
        name="🔗 Useful Links",
        value=(
            f"• **Changelog Channel**: <#{config.CHANGELOG_CHANNEL_ID}>\n"
            f"• **Main Channel**: <#{config.TARGET_CHANNEL_ID}>\n"
            "• **Use `/help`** to see all available commands"
        ),
        inline=False
    )

    embed.timestamp = datetime.now(IRAN_TIMEZONE)
    embed.set_footer(text="GW2 Bot Changelog • Use /help for command list")

    await ctx.respond(embed=embed)

# Removed setup_hook - using on_ready instead for better compatibility

@bot.event
async def on_ready():
    """Called when the bot is ready"""
    global api_manager, timer_task

    print(f'✅ Logged in as {bot.user} (ID: {bot.user.id})')
    print('------')

    # Initialize API manager if not already done
    if api_manager is None:
        print("🔧 Initializing API Manager...")
        api_manager = GW2APIManager()
        print("✅ API Manager initialized")

    # Start timer loop if not already running
    if timer_task is None or timer_task.done():
        print("🚀 Starting boss timer loop...")
        timer_task = bot.loop.create_task(boss_timer_loop())
        print(f"✅ Timer task created: {timer_task}")

    # Initialize item cache in background
    print("📦 Initializing item cache...")
    bot.loop.create_task(update_item_cache())

    # Set custom status
    activity = discord.Activity(
        type=discord.ActivityType.watching,
        name=f"for world bosses & items | /help"
    )
    await bot.change_presence(activity=activity)

    # Send startup notification to configured channel
    try:
        channel = bot.get_channel(config.TARGET_CHANNEL_ID)
        if channel:
            embed = discord.Embed(
                title="🤖 Bot Online",
                description="GW2 World Boss Timer is now online with enhanced features!",
                color=0x00ff00
            )
            embed.add_field(
                name="🔔 Auto Alerts Enabled",
                value="You'll receive notifications 10min, 5min, and at spawn time",
                inline=False
            )
            embed.add_field(
                name="🆕 New Features",
                value="• `/item [name]` - Get detailed item information\n• `/price [name]` - Get trading post prices\n• Both commands support autocomplete!",
                inline=False
            )
            embed.timestamp = datetime.now(IRAN_TIMEZONE)
            embed.set_footer(text="Use /help to see available commands")
            await channel.send(embed=embed)
            print(f"✅ Sent startup notification to channel {config.TARGET_CHANNEL_ID}")
    except Exception as e:
        print(f"⚠️ Could not send startup notification: {e}")

    # Send changelog notification for new features
    try:
        await send_changelog_notification(
            title="Bot Updated - New Item & Price Commands!",
            description="The GW2 Bot has been enhanced with powerful new item lookup features!",
            changes=[
                "**New `/item [name]` command** - Get comprehensive item information including stats, rarity, vendor value, and trading post prices",
                "**New `/price [name]` command** - Get detailed trading post analysis with profit calculations, market status, and trading tips",
                "**Autocomplete support** - Both commands provide intelligent suggestions as you type item names",
                "**Smart item search** - Search through 69,000+ items with fuzzy matching and partial name support",
                "**Rich visual embeds** - Color-coded by item rarity with item icons and organized information",
                "**Real-time price data** - Current buy/sell orders, market volume, and profit calculations after TP tax",
                "**Enhanced help system** - Updated `/help` command with new features and better organization",
                "**Improved command visibility** - All commands now properly registered and visible in Discord",
                "**Changelog system** - New `/changelog` command and automatic update notifications"
            ],
            version="v2.1.0"
        )
    except Exception as e:
        print(f"⚠️ Could not send changelog notification: {e}")

    print("🔔 Auto alerts are now active!")
    print(f"📍 Monitoring channel: {config.TARGET_CHANNEL_ID}")
    print(f"📋 Changelog channel: {config.CHANGELOG_CHANNEL_ID}")
    print("🚀 Bot is ready!")

@bot.event
async def on_application_command_error(ctx, error):
    """Handle slash command errors"""
    print(f"❌ Command error in {ctx.command}: {error}")

    embed = discord.Embed(
        title="❌ Command Error",
        description="An error occurred while processing your command.",
        color=0xff0000
    )

    if isinstance(error, commands.CommandOnCooldown):
        embed.description = f"Command is on cooldown. Try again in {error.retry_after:.1f} seconds."
    elif isinstance(error, commands.MissingPermissions):
        embed.description = "You don't have permission to use this command."
    else:
        embed.description = "An unexpected error occurred. Please try again later."

    try:
        await ctx.respond(embed=embed, ephemeral=True)
    except:
        pass  # If we can't respond, just log the error

@bot.event
async def on_disconnect():
    """Cleanup when the bot disconnects"""
    print("⚠️ Bot disconnected from Discord")
    global api_manager
    if api_manager:
        await api_manager.close()

@bot.event
async def on_shutdown():
    """Cleanup when the bot shuts down"""
    print("🔄 Bot shutting down...")
    global api_manager
    if api_manager:
        await api_manager.close()
    await bot.http.close()

def main():
    # Load environment variables from .env file
    load_dotenv()

    # Get the bot token from environment variable
    TOKEN = os.getenv("DISCORD_TOKEN")

    if not TOKEN:
        print("❌ Error: No DISCORD_TOKEN found in environment variables or .env file")
        print("💡 Please create a .env file with: DISCORD_TOKEN=your_bot_token_here")
        return

    print("🚀 Starting Guild Wars 2 World Boss Discord Bot...")
    print(f"📍 Target Guild ID: {config.TARGET_GUILD_ID}")
    print(f"📍 Target Channel ID: {config.TARGET_CHANNEL_ID}")

    # Set up signal handlers for graceful shutdown
    import signal

    def handle_shutdown(signum, frame):
        print("\n🔄 Shutting down gracefully...")
        # This will trigger the cleanup in the close() handler
        bot.loop.create_task(bot.close())

    # Register signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        signal.signal(sig, handle_shutdown)

    # Start the bot
    try:
        bot.run(TOKEN)
    except KeyboardInterrupt:
        print("\n🔄 Bot stopped by user")
    except discord.LoginFailure:
        print("❌ Invalid Discord token! Please check your .env file")
        sys.exit(1)
    except Exception as e:
        current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S Iran Time")
        print("\n" + "=" * 60)
        print("💥 UNEXPECTED ERROR OCCURRED")
        print("=" * 60)
        print(f"📅 Error Time: {current_time}")
        print(f"❌ Error: {e}")
        print(f"🔍 Error Type: {type(e).__name__}")
        traceback.print_exc()
        print("🔄 Bot will attempt to restart...")
        print("=" * 60)
        sys.exit(1)

# Run the main function
if __name__ == "__main__":
    main()
